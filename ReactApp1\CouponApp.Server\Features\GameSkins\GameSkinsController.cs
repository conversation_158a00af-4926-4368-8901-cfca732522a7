using CouponApp.Server.Extensions;
using Mediator;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace CouponApp.Server.Features.GameSkins;

[ApiController]
[Route("api/game-skins")]
[Authorize]
public class GameSkinsController : ControllerBase
{
    private readonly IMediator _mediator;

    public GameSkinsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    public async Task<IActionResult> SaveGameSkin([FromBody] CreateGameSkinDto createDto)
    {
        var userId = User.GetId();
        var command = new SaveGameSkinCommand(createDto, userId);
        var result = await _mediator.Send(command);
        
        return result.Match<IActionResult>(
            gameSkin => Ok(gameSkin),
            error => BadRequest(error.Value)
        );
    }

    [HttpGet]
    public async Task<IActionResult> GetGameSkins([FromQuery] string? gameId, [FromQuery] Guid? organizationId)
    {
        var userId = User.GetId();
        var query = new GetGameSkinsQuery(gameId, organizationId, userId);
        var result = await _mediator.Send(query);
        
        return result.Match<IActionResult>(
            gameSkins => Ok(gameSkins),
            unauthorized => Unauthorized(unauthorized.Message)
        );
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetGameSkinById(Guid id)
    {
        var userId = User.GetId();
        var query = new GetGameSkinByIdQuery(id, userId);
        var result = await _mediator.Send(query);
        
        return result.Match<IActionResult>(
            gameSkin => Ok(gameSkin),
            notFound => NotFound(),
            unauthorized => Unauthorized(unauthorized.Message)
        );
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteGameSkin(Guid id)
    {
        var userId = User.GetId();
        var command = new DeleteGameSkinCommand(id, userId);
        var result = await _mediator.Send(command);
        
        return result.Match<IActionResult>(
            success => NoContent(),
            notFound => NotFound(),
            unauthorized => Unauthorized(unauthorized.Message)
        );
    }
}
