using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using CouponApp.Server.Models.Entities;

namespace CouponApp.Server.Features.GameSkins;

public class GameSkin : BaseEntity
{
    [Required]
    public string Name { get; set; }

    [Required]
    public string GameId { get; set; }

    [Required]
    [Column(TypeName = "jsonb")]
    public object ConfigOverrides { get; set; }

    public Guid? OrganizationId { get; set; }
}
