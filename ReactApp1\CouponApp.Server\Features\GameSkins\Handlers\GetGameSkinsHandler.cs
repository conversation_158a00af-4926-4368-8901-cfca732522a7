using CouponApp.Server.Data;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;

namespace CouponApp.Server.Features.GameSkins.Handlers;

public class GetGameSkinsHandler : IRequestHandler<GetGameSkinsQuery, OneOf<GameSkinsResponseDto, UnauthorizedError>>
{
    private readonly ApplicationDbContext _context;

    public GetGameSkinsHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<GameSkinsResponseDto, UnauthorizedError>> Handle(GetGameSkinsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var query = _context.GameSkins.AsQueryable();

            // Filter by GameId if provided
            if (!string.IsNullOrWhiteSpace(request.GameId))
            {
                query = query.Where(gs => gs.GameId == request.GameId);
            }

            // Filter by OrganizationId if provided
            if (request.OrganizationId.HasValue)
            {
                query = query.Where(gs => gs.OrganizationId == request.OrganizationId.Value);
            }

            // Also include public skins (where OrganizationId is null)
            if (request.OrganizationId.HasValue)
            {
                query = query.Where(gs => gs.OrganizationId == request.OrganizationId.Value || gs.OrganizationId == null);
            }
            else
            {
                // If no organization specified, only return public skins
                query = query.Where(gs => gs.OrganizationId == null);
            }

            var gameSkins = await query
                .OrderBy(gs => gs.CreatedAt)
                .Select(gs => new GameSkinDto
                {
                    Id = gs.Id,
                    Name = gs.Name,
                    GameId = gs.GameId,
                    ConfigOverrides = gs.ConfigOverrides,
                    OrganizationId = gs.OrganizationId
                })
                .ToListAsync(cancellationToken);

            return new GameSkinsResponseDto
            {
                GameSkins = gameSkins
            };
        }
        catch (Exception)
        {
            return new UnauthorizedError("Failed to retrieve game skins");
        }
    }
}
