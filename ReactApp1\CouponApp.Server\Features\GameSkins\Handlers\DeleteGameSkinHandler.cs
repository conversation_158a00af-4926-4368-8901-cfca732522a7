using CouponApp.Server.Data;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.GameSkins.Handlers;

public class DeleteGameSkinHandler : IRequestHandler<DeleteGameSkinCommand, OneOf<Success, NotFound, UnauthorizedError>>
{
    private readonly ApplicationDbContext _context;

    public DeleteGameSkinHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<Success, NotFound, UnauthorizedError>> Handle(DeleteGameSkinCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var gameSkin = await _context.GameSkins
                .FirstOrDefaultAsync(gs => gs.Id == request.Id, cancellationToken);

            if (gameSkin == null)
            {
                return new NotFound();
            }

            // For now, allow deletion without strict authorization checks
            // In the future, you might want to check if the user has permission to delete this skin
            // based on OrganizationId or ownership

            _context.GameSkins.Remove(gameSkin);
            await _context.SaveChangesAsync(cancellationToken);

            return new Success();
        }
        catch (Exception)
        {
            return new UnauthorizedError("Failed to delete game skin");
        }
    }
}
