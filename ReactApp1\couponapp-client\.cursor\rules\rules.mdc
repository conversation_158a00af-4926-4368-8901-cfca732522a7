---
description: 
globs: *.ts,*.tsx
alwaysApply: false
---
<rule>
Applies to folder: /apps/games/
When you work with controls/config keys in a game (like scratch-to-win game or game-boilerplate or 2048 and more...)  keep in mind that we might need to also edit files like: config (that is main config file) as well as config key editor, main config editoir and the controls itself if the game is using this, usally located at game/src/components.
Need to look if scenes are using these changes too. You will need to check references to the changes.
</rule>

<rule>
Applies to folder: /apps/games/
APPLIES TO config.ts
IMPORTANT GUIDELINES FOR MODYFYING CONFIG.TS

When you add some type like "GameButtonStyle" make sure you implement it correctly. For example it might require special ConfigEditorType (gameConfig.ts shared file)
You might need to add a new ConfigEditorType for the new style definition and implement settings handler (and component) in ConfigKeyEditor. If you plan to  have different settings in new type definition then it is crucial, we must have correct editor for your newly created type.
</rule>

<rule>
Applies to: CREATING NEW GAME OR COPYING BOILERPLATE
When we create a game from a boilerplate you will need to look for usages, where the boilerplate was referenced, eg in our game registres, game selector widgets etc.

Rename everything in index file from game boilerplate to a new name of our new game (and in other places that apply, but not config.ts)

In game registry do not replace name of game boilerplate, but instead add a new entry of newly created game. Make sure we renamed our game in the new, copied game directory and these ids needs to match in index.ts file. Also, do not rename config.ts in that case and anything in config file.
</rule>

<rule>
Applies to folder: /apps/games/*
When we are editing UI of the game, eg using components etc make sure you add correct data attributes for each component that is related to a specific config key in config.ts
So for exmaple if you create a button, you need to set correct config key of it. Some components have it taking as prop already, but some might not if we create a custom component (lets say you create a simple div, then you need to use data-editor-selectable-key to inform our editor that this div is selectable)
</rule>

<rule>
Applies to widget settings components and config key editors.
Stop using || operator to fallback on numeric values. In most cases this will lead to an error where for example I want to set padding 0 then a fallbacks is being triggered because 0 acts as false. 
</rule>

<rule>
Applies when copying diectories or files.
Keep in mind that our root directory of this project is "C:\Users\<USER>\RiderProjects\ReactIdentity\ReactApp1\couponapp-client"
Keep in mind that we use Powershell on Windows 11.

When you copy for example a game, DO NOT use relative paths. Use absolute paths instead.
</rule>

<rule>
Applies to: Games and their state management
useGameState hook keeps things in local storage. Also, consider using useGameState hook that are leveraging useGameState, instead of directly using useGameState.
</rule>

<rule>
Keep in mind that we are using dark mode for our dashboard. Ensure colors properly fit in dark mode.
</rule>

<rule>
Do not add unnecessary comments to code. Add them only if there is something not usual or complex. In other case, just write code that is self commenting by using correct naming convetions etc.
</rule>

<rule>
# AI Design Tutorial Prompt

You are an expert UI/UX designer with over a decade of experience. When providing design feedback or creating designs, follow this structured approach based on design maturity levels:

## Core Design Principles to Evaluate (in order):

### 1. **Copywriting & Content**
- Use clear, concise language - eliminate unnecessary words
- Ensure button text matches the actual action being performed
- When unsure, use ChatGPT with context: "Make this copy clear but short"
- Advanced: Avoid repeating words already established by headings/context

### 2. **Visual Hierarchy & Communication**
- Visuals should primarily serve communication, not decoration
- If unsure between flashy vs simple, always choose simplicity
- Focus attention strategically - use visual patterns to connect related UI elements
- Advanced: Add emphasis only to what truly matters

### 3. **Color Usage (60-30-10 Rule)**
- 60% neutral colors (white, light gray)
- 30% complementary colors (black)
- 10% brand/accent colors
- Avoid overusing strong colors (causes stress and nothing gets attention)
- Use color strategically to assign meaning and guide focus

### 4. **Typography System**
- Maximum 4 font sizes and 2 font weights
- Use monospace fonts for numbers that may change/grow
- Maintain consistency across similar elements
- Reduce variety to create cleaner, more professional appearance

### 5. **Spacing & Alignment (8-Point Grid System)**
- All spacing values must be divisible by 8 or 4
- Instead of random numbers (25px, 11px), use calculated spacing (24px, 12px)
- Group related elements logically
- Maintain deliberate spacing both between and inside elements

## Design Maturity Levels:

### **Beginner Level**
- Focus on fixing basic issues first
- Prioritize clarity over creativity
- Master the fundamentals before attempting advanced techniques

### **Junior Level (1-3 years)**
- Understand that less can be more
- Avoid over-complicating with excessive effects
- Build systematic approaches to spacing and color

### **Mid-Level (3-6 years)**
- Can execute complex visuals successfully
- Understands color relationships and shading/tinting
- Implements consistent design systems

### **Senior Level (7+ years)**
- Achieves more with less
- Every design decision serves a purpose
- Creates cohesive experiences across entire applications
- Masters the art of strategic emphasis and visual connections

## The Hidden Advanced Principle:
**Think Beyond Static Screens** - Design experiences like movies, not just pretty pictures. Consider how screens connect and flow to create memorable, delightful user journeys.

## Evaluation Method:
For each design element, score as:
- **Fail**: Needs significant improvement
- **Half Pass**: Moving in right direction but needs refinement  
- **Full Pass**: Meets professional standards

Always provide specific, actionable feedback with exact measurements and suggestions for improvement. Reference real-world examples when possible.

</rule>