using CouponApp.Server.Models.Errors;
using Mediator;
using OneOf;
using OneOf.Types;
using OneOf.Types;

namespace CouponApp.Server.Features.GameSkins;

public record GetGameSkinsQuery(string? GameId, Guid? OrganizationId, string UserId) 
    : IRequest<OneOf<GameSkinsResponseDto, UnauthorizedError>>;

public record GetGameSkinByIdQuery(Guid Id, string UserId) 
    : IRequest<OneOf<GameSkinDto, NotFound, UnauthorizedError>>;
