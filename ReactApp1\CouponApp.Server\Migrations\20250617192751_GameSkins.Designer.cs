﻿// <auto-generated />
using System;
using System.Collections.Generic;
using CouponApp.Server.Data;
using CouponApp.Server.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CouponApp.Server.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250617192751_GameSkins")]
    partial class GameSkins
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CouponApp.Server.Data.UserProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("UserProfiles");
                });

            modelBuilder.Entity("CouponApp.Server.Features.GameSkins.GameSkin", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<object>("ConfigOverrides")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("GameId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("GameSkins");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Campaign", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CampaignAnalyticsId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CampaignDomainSettingsId")
                        .HasColumnType("uuid");

                    b.Property<CampaignConfigDto>("Config")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<CampaignConfigDto>("DraftConfig")
                        .HasColumnType("jsonb");

                    b.Property<bool?>("HasNoCouponsLeft")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("WorkspaceId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CampaignAnalyticsId")
                        .IsUnique();

                    b.HasIndex("CampaignDomainSettingsId");

                    b.HasIndex("WorkspaceId");

                    b.ToTable("Campaigns");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignAnalytics", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TotalClicks")
                        .HasColumnType("integer");

                    b.Property<int>("UniqueClicks")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("CampaignAnalytics");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignDomainSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CustomDomainId")
                        .HasColumnType("uuid");

                    b.Property<string>("Slug")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.HasIndex("CustomDomainId");

                    b.ToTable("CampaignDomainSettings");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignSession", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BrowserFingerprint")
                        .HasColumnType("text");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<string>("LocalStorageValue")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.ToTable("CampaignSessions");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignUniqueEnter", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BrowserFingerprint")
                        .HasColumnType("text");

                    b.Property<Guid>("CampaignAnalyticsId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EnteredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<string>("LocalStorageValue")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CampaignAnalyticsId");

                    b.ToTable("CampaignUniqueEnters");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Coupon", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CouponPackId")
                        .HasColumnType("uuid");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CouponPackId");

                    b.ToTable("Coupons");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CouponPack", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("DeleteCouponOnRedeem")
                        .HasColumnType("boolean");

                    b.Property<int?>("InterruptThreshold")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("NotifyThreshold")
                        .HasColumnType("integer");

                    b.Property<bool>("ShouldCheckingForUserParticipation")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.ToTable("CouponPacks");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CustomDomain", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CloudflareHostnameId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DomainHostnameHandlerType")
                        .HasColumnType("integer");

                    b.Property<string>("DomainName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("HasValidSsl")
                        .HasColumnType("boolean");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("CustomDomains");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Invitation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InvitationToken")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("InvitedByUserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("InvitedByUserId");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Invitations");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.LeadForm", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Dictionary<string, object>>("FormData")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.ToTable("LeadForms");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Organization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ShortId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Organizations");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.OrganizationAsset", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("OrganizationAssets");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.OrganizationIntegration", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text");

                    b.Property<Dictionary<string, object>>("AdditionalData")
                        .HasColumnType("jsonb");

                    b.Property<string>("ConnectionId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("IntegrationType")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("OrganizationIntegrations");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.UserOrganization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Role")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserProfileId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.HasIndex("UserProfileId");

                    b.ToTable("UserOrganizations");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Campaign", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.CampaignAnalytics", "CampaignAnalytics")
                        .WithOne("Campaign")
                        .HasForeignKey("CouponApp.Server.Models.Entities.Campaign", "CampaignAnalyticsId");

                    b.HasOne("CouponApp.Server.Models.Entities.CampaignDomainSettings", "CampaignDomainSettings")
                        .WithMany()
                        .HasForeignKey("CampaignDomainSettingsId");

                    b.HasOne("CouponApp.Server.Models.Entities.Organization", "Workspace")
                        .WithMany()
                        .HasForeignKey("WorkspaceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CampaignAnalytics");

                    b.Navigation("CampaignDomainSettings");

                    b.Navigation("Workspace");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignDomainSettings", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Campaign", "Campaign")
                        .WithMany()
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CouponApp.Server.Models.Entities.CustomDomain", "CustomDomain")
                        .WithMany()
                        .HasForeignKey("CustomDomainId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");

                    b.Navigation("CustomDomain");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignSession", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Campaign", "Campaign")
                        .WithMany()
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignUniqueEnter", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.CampaignAnalytics", "CampaignAnalytics")
                        .WithMany("UniqueEnters")
                        .HasForeignKey("CampaignAnalyticsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CampaignAnalytics");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Coupon", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.CouponPack", "CouponPack")
                        .WithMany("Coupons")
                        .HasForeignKey("CouponPackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CouponPack");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CouponPack", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Campaign", "Campaign")
                        .WithMany("CouponPacks")
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CustomDomain", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Organization", "Organization")
                        .WithMany()
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Invitation", b =>
                {
                    b.HasOne("CouponApp.Server.Data.UserProfile", "InvitedByUser")
                        .WithMany()
                        .HasForeignKey("InvitedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CouponApp.Server.Models.Entities.Organization", "Organization")
                        .WithMany()
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InvitedByUser");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.LeadForm", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Campaign", "Campaign")
                        .WithMany()
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.OrganizationAsset", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Organization", "Organization")
                        .WithMany("OrganizationAssets")
                        .HasForeignKey("OrganizationId");

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.OrganizationIntegration", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Organization", "Organization")
                        .WithMany("Integrations")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.UserOrganization", b =>
                {
                    b.HasOne("CouponApp.Server.Models.Entities.Organization", "Organization")
                        .WithMany("Members")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CouponApp.Server.Data.UserProfile", "UserProfile")
                        .WithMany()
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organization");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Campaign", b =>
                {
                    b.Navigation("CouponPacks");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CampaignAnalytics", b =>
                {
                    b.Navigation("Campaign")
                        .IsRequired();

                    b.Navigation("UniqueEnters");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.CouponPack", b =>
                {
                    b.Navigation("Coupons");
                });

            modelBuilder.Entity("CouponApp.Server.Models.Entities.Organization", b =>
                {
                    b.Navigation("Integrations");

                    b.Navigation("Members");

                    b.Navigation("OrganizationAssets");
                });
#pragma warning restore 612, 618
        }
    }
}
