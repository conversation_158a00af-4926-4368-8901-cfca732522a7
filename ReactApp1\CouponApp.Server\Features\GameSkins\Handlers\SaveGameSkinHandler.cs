using CouponApp.Server.Data;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.GameSkins.Handlers;

public class SaveGameSkinHandler : IRequestHandler<SaveGameSkinCommand, OneOf<GameSkinDto, Error<string>>>
{
    private readonly ApplicationDbContext _context;

    public SaveGameSkinHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<GameSkinDto, Error<string>>> Handle(SaveGameSkinCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(request.CreateDto.Name))
            {
                return new Error<string>("Name is required");
            }

            if (string.IsNullOrWhiteSpace(request.CreateDto.GameId))
            {
                return new Error<string>("GameId is required");
            }

            if (request.CreateDto.ConfigOverrides == null)
            {
                return new Error<string>("ConfigOverrides is required");
            }

            // Create new GameSkin entity
            var gameSkin = new GameSkin
            {
                Id = Guid.NewGuid(),
                Name = request.CreateDto.Name.Trim(),
                GameId = request.CreateDto.GameId.Trim(),
                ConfigOverrides = request.CreateDto.ConfigOverrides,
                OrganizationId = request.CreateDto.OrganizationId
            };

            _context.GameSkins.Add(gameSkin);
            await _context.SaveChangesAsync(cancellationToken);

            // Return DTO
            var dto = new GameSkinDto
            {
                Id = gameSkin.Id,
                Name = gameSkin.Name,
                GameId = gameSkin.GameId,
                ConfigOverrides = gameSkin.ConfigOverrides,
                OrganizationId = gameSkin.OrganizationId
            };

            return dto;
        }
        catch (Exception ex)
        {
            return new Error<string>($"Failed to save game skin: {ex.Message}");
        }
    }
}
