﻿using CouponApp.Server.Features.GameSkins;
using CouponApp.Server.Models.Entities;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace CouponApp.Server.Data
{
    public class ApplicationDbContext : DbContext
    {
        public DbSet<Campaign> Campaigns { get; set; }
        public DbSet<CustomDomain> CustomDomains { get; set; }
        public DbSet<CouponPack> CouponPacks { get; set; }
        public DbSet<Coupon> Coupons { get; set; }
        public DbSet<Organization> Organizations { get; set; }
        public DbSet<Invitation> Invitations { get; set; }
        public DbSet<LeadForm> LeadForms { get; set; }
        public DbSet<OrganizationIntegration> OrganizationIntegrations { get; set; }
        public DbSet<CampaignAnalytics> CampaignAnalytics { get; set; }
        public DbSet<CampaignUniqueEnter> CampaignUniqueEnters { get; set; }
        public DbSet<OrganizationAsset> OrganizationAssets { get; set; }
        public DbSet<UserProfile> UserProfiles { get; set; }
        public DbSet<UserOrganization> UserOrganizations { get; set; }
        public DbSet<GameSkin> GameSkins { get; set; }

        public DbSet<CampaignSession> CampaignSessions { get; set; }
        public DbSet<CampaignDomainSettings> CampaignDomainSettings { get; set; }
        
        
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            
            builder.Entity<Campaign>()
                .HasOne(c => c.CampaignAnalytics)
                .WithOne(a => a.Campaign)
                .HasForeignKey<Campaign>(c => c.CampaignAnalyticsId);

            builder.Entity<CampaignAnalytics>()
                .HasOne(ca => ca.Campaign)
                .WithOne(c => c.CampaignAnalytics)
                .HasForeignKey<Campaign>(c => c.CampaignAnalyticsId);
            
            // builder.Entity<Organization>()
            //     .HasOne(o => o.Owner)
            //     .WithMany(u => u.OwnedOrganizations)
            //     .HasForeignKey(o => o.OwnerId)
            //     .OnDelete(DeleteBehavior.Restrict);

            // builder.Entity<Organization>()
            //     .HasMany(o => o.Members)
            //     .WithMany(u => u.Organizations)
            //     .UsingEntity(j => j.ToTable("UserOrganizations"));
            
            // builder.Entity<Project>()
            //     .HasMany(p => p.TeamMembers)
            //     .WithMany(u => u.Projects)
            //     .UsingEntity(j => j.ToTable("ProjectTeamMembers"));
            //
            // builder.Entity<Project>()
            //     .HasOne(p => p.Owner)
            //     .WithMany(u => u.OwnedProjects)
            //     .HasForeignKey(p => p.OwnerId)
            //     .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
