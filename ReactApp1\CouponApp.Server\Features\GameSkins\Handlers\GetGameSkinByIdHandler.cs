using CouponApp.Server.Data;
using CouponApp.Server.Models.Errors;
using Mediator;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;

namespace CouponApp.Server.Features.GameSkins.Handlers;

public class GetGameSkinByIdHandler : IRequestHandler<GetGameSkinByIdQuery, OneOf<GameSkinDto, NotFound, UnauthorizedError>>
{
    private readonly ApplicationDbContext _context;

    public GetGameSkinByIdHandler(ApplicationDbContext context)
    {
        _context = context;
    }

    public async ValueTask<OneOf<GameSkinDto, NotFound, UnauthorizedError>> Handle(GetGameSkinByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var gameSkin = await _context.GameSkins
                .FirstOrDefaultAsync(gs => gs.Id == request.Id, cancellationToken);

            if (gameSkin == null)
            {
                return new NotFound();
            }

            var dto = new GameSkinDto
            {
                Id = gameSkin.Id,
                Name = gameSkin.Name,
                GameId = gameSkin.GameId,
                ConfigOverrides = gameSkin.ConfigOverrides,
                OrganizationId = gameSkin.OrganizationId
            };

            return dto;
        }
        catch (Exception)
        {
            return new UnauthorizedError("Failed to retrieve game skin");
        }
    }
}
