<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName="IIS Express">ReactApp1.Server/ReactApp1.Server.csproj</projectFile>
    <projectFile profileName="http">ReactApp1.Server/ReactApp1.Server.csproj</projectFile>
    <projectFile profileName="https">ReactApp1.Server/ReactApp1.Server.csproj</projectFile>
    <projectFile kind="EcmaScript">couponapp-client/couponapp-client.esproj</projectFile>
    <projectFile kind="EcmaScript">reactapp1.client/reactapp1.client.esproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d87ee4e5-63ef-49c6-af94-15843f713018" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/BackgroundServices/CustomDomainVerificationService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CustomDomains/CustomDomainVerificationService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/AnalyticsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CampaignStats/AnalyticsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/CampaignsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/CouponPacksController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CouponPacks/CouponPacksController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/CustomDomainsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CustomDomains/CustomDomainsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/FileUploadController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/FileUpload/FileUploadController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/HealthController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/HealthCheck/HealthController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/InvitationsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Invitations/InvitationsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/LeadFormsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/LeadForms/LeadFormsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/Organizations/OrganizationAssetsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/OrganizationAssets/OrganizationAssetsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/Organizations/OrganizationIntegrationsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationIntegrationsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/Organizations/OrganizationsCampaignsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationsCampaignsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/Organizations/OrganizationsController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationsController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/ProfileController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/TestController.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/Webhooks/LogtoWebhooksController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/LogtoAuth/LogtoWebhooksController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Controllers/Webhooks/NangoWebhooksController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Nango/NangoWebhooksController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/CouponApp.Server.csproj" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/CouponApp.Server.csproj" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Data/ApplicationDbContext.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Data/ApplicationDbContext.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/Handlers/GetPublicCampaignHandler.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/Handlers/GetPublicCampaignHandler.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Features/OrganizationAssets/Handlers/GetOrganizationAssetsHandler.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/OrganizationAssets/Handlers/GetOrganizationAssetsHandler.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/ApplicationUserMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/CampaignConfigMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignConfigMapper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/CampaignDomainSettingsMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CustomDomains/CampaignDomainSettingsMapper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/CampaignFlowNodeMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignFlowNodeMapper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/CampaignMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignMapper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/CouponPackMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CouponPacks/CouponPackMapper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/OrganizationIntegrationMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationIntegrationMapper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Mappers/OrganizationMapper.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationMapper.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Migrations/ApplicationDbContextModelSnapshot.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Migrations/ApplicationDbContextModelSnapshot.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/CampaignNodeType.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignNodeType.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/AddOrganizationIntegrationDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/AddOrganizationIntegrationDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/AddTeamMemberDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Invitations/AddTeamMemberDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/ApplicationUserDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/CampaignAnalyticsDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CampaignStats/Handlers/CampaignAnalyticsDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/CampaignEditorDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignEditorDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/CouponPackCountDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CouponPacks/CouponPackCountDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/CouponPackDtos.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CouponPacks/CouponPackDtos.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/CustomDomainDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CustomDomains/CustomDomainDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/ExecuteNodeRequest.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/ExecuteNodeRequest.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Hubspot/HubspotDtos.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Hubspot/HubspotDtos.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/InvitationDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Invitations/InvitationDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/LeadFormSubmissionDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/LeadForms/LeadFormSubmissionDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Logto/LogtoWebhookRequest.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/LogtoAuth/LogtoWebhookRequest.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Mailchimp/MailChimpSearchMembersResponseDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Mailchimp/MailChimpSearchMembersResponseDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Mailchimp/MailchimpListsResponseDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Mailchimp/MailchimpListsResponseDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/NangoConnectionCreateData.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Nango/NangoConnectionCreateData.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Organization/OrganizationAssetDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/OrganizationAssets/OrganizationAssetDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/OrganizationDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/OrganizationIntegrationDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationIntegrationDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/OrganizationMembersResponse.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationMembersResponse.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Plausible/PlausibleRequestDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Plausible/PlausibleRequestDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/PublicCampaignDataDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/PublicCampaignDataDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Requests/CreateCustomDomainRequest.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CustomDomains/CreateCustomDomainRequest.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Shopify/ShopifyCustomerCreateRequest.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Shopify/ShopifyCustomerCreateRequest.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Shopify/ShopifyCustomerCreateResponse.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Shopify/ShopifyCustomerCreateResponse.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/Shopify/ShopifyCustomerDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Shopify/ShopifyCustomerDto.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/DTOs/UserProfileDto.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/BaseEntity.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Data/BaseEntity.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/Campaign.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/Campaign.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/CampaignAnalytics.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CampaignStats/Handlers/CampaignAnalytics.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/CampaignSession.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignSession.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/CampaignUniqueEnter.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CampaignStats/CampaignUniqueEnter.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/CouponPack.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CouponPacks/CouponPack.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/CustomDomain.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CustomDomains/CustomDomain.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/Invitation.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Invitations/Invitation.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/LeadForm.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/LeadForms/LeadForm.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/Organization.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/Organization.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/OrganizationAsset.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/OrganizationAssets/OrganizationAsset.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/OrganizationIntegration.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Organizations/OrganizationIntegration.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/UserOrganization.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Entities/UserProfile.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Enums/CampaignSaveMode.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/CampaignSaveMode.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Enums/CustomDomainStatus.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CustomDomains/CustomDomainStatus.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Errors/CouponPackFullError.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CouponPacks/CouponPackFullError.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Errors/StringError.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Errors/StringError.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Errors/UnauthorizedError.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Errors/UnauthorizedError.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Errors/UserAlreadyParticipated.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Errors/UserAlreadyParticipated.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/Errors/UserNotFound.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Errors/UserNotFound.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Models/SessionFingerprint.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Campaigns/SessionFingerprint.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Services/CampaignSessionService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/CampaignStats/CampaignSessionService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Services/LogtoUserService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/LogtoAuth/LogtoUserService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Services/NangoConnectionIdService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Nango/NangoConnectionIdService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Services/PlausibleService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Plausible/PlausibleService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Services/UserManagementService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/Services/UserProfileService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/CouponApp.Server/Features/Users/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/CouponApp.Server/WeatherForecast.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/couponapp-client/apps/dashboard/src/Pages/campaign/editor/_components/widget-settings/gameWidgetConfig.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/couponapp-client/apps/dashboard/src/Pages/campaign/editor/_components/widget-settings/gameWidgetConfig.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/couponapp-client/games/2048/src/editor/MainConfigEditor.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/couponapp-client/games/2048/src/editor/MainConfigEditor.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/couponapp-client/games/quiz-game/src/editor/MainConfigEditor.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/couponapp-client/games/quiz-game/src/editor/MainConfigEditor.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/couponapp-client/games/quiz-game/src/types/config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/couponapp-client/games/quiz-game/src/types/config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/couponapp-client/games/shared-game-utils/hooks/useConfigType.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/couponapp-client/games/shared-game-utils/hooks/useConfigType.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/couponapp-client/packages/shared/lib/game/gameConfig.ts" beforeDir="false" afterPath="$PROJECT_DIR$/couponapp-client/packages/shared/lib/game/gameConfig.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="features/new-responsiveness" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;kret126&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/kret126/CouponApp.git&quot;,
    &quot;accountId&quot;: &quot;7cc55065-dffc-472c-918e-ed92ae5ff2ec&quot;
  },
  &quot;recentNewPullRequestHead&quot;: {
    &quot;server&quot;: {
      &quot;useHttp&quot;: false,
      &quot;host&quot;: &quot;github.com&quot;,
      &quot;port&quot;: null,
      &quot;suffix&quot;: null
    },
    &quot;owner&quot;: &quot;kret126&quot;,
    &quot;repository&quot;: &quot;CouponApp&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/2854ce6d56c18d0d837d3a3ef9c4f2c7c77691fa3528c8394986ac7ce7719/StackFrameIterator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/92f879b14c3424dd149e6985f9a6baf181248b4b7a17628f3d030b1a79934/Guid.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/bd1d5c50194fea68ff3559c160230b0ab50f5acf4ce3061bffd6d62958e2182/ExceptionDispatchInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/CouponApp.Server/Features/OrganizationAssets/Handlers/GetOrganizationAssetsHandler.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/couponapp-client/package-lock.json" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2igvgZI0m0OJ9XSW5SNChG5Brqp" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.ReactApp1.Server: http.executor": "Debug",
    ".NET Launch Settings Profile.ReactApp1.Server: https.executor": "Debug",
    "Publish to folder.Publish CouponApp.Server to folder.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "XThreadsFramesViewSplitterKey": "0.40088105",
    "git-widget-placeholder": "dev",
    "ignore.virus.scanning.warn.message": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "ml.llm.LLMConfigurable",
    "ts.external.directory.path": "C:\\Users\\<USER>\\RiderProjects\\ReactIdentity\\ReactApp1\\couponapp-client\\packages\\shared\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "sqlserver",
      "sqlserver.localdb",
      "mysql",
      "postgresql"
    ]
  }
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.ReactApp1.Server: http">
    <configuration name="Publish CouponApp.Server to folder" type="DotNetFolderPublish" factoryName="Publish to folder">
      <riderPublish configuration="Release" platform="Any CPU" runtime="Portable" target_folder="$PROJECT_DIR$/CouponApp.Server/bin/Release/net8.0/publish" target_framework="net8.0" uuid_high="3394321316623634340" uuid_low="-5734820233516949860" />
      <method v="2" />
    </configuration>
    <configuration name="CouponApp.Server: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/CouponApp.Server/CouponApp.Server.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="CouponApp.Server: https" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/CouponApp.Server/CouponApp.Server.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="https" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="ReactApp1.Server: http" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/CouponApp.Server/CouponApp.Server.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="http" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="CouponApp.Server/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="imageTag" value="couponapp.server" />
          <option name="containerName" value="couponapp.server" />
          <option name="contextFolderPath" value="C:\Users\<USER>\RiderProjects\ReactIdentity\ReactApp1" />
          <option name="portBindings">
            <list>
              <DockerPortBindingImpl>
                <option name="containerPort" value="8080" />
                <option name="hostIp" value="127.0.0.1" />
                <option name="hostPort" value="8080" />
              </DockerPortBindingImpl>
            </list>
          </option>
          <option name="sourceFilePath" value="CouponApp.Server/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/couponapp-client/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue=".NET Launch Settings Profile.CouponApp.Server: IIS Express" />
      <item itemvalue=".NET Launch Settings Profile.CouponApp.Server: https" />
      <item itemvalue=".NET Launch Settings Profile.ReactApp1.Server: http" />
      <item itemvalue="Docker.CouponApp.Server/Dockerfile" />
      <item itemvalue="npm.dev" />
      <item itemvalue="Publish to folder.Publish CouponApp.Server to folder" />
    </list>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d87ee4e5-63ef-49c6-af94-15843f713018" name="Changes" comment="" />
      <created>1719925147918</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1719925147918</updated>
      <workItem from="1719925148846" duration="2055000" />
      <workItem from="1719939539937" duration="1149000" />
      <workItem from="1719944154076" duration="1083000" />
      <workItem from="1719946045614" duration="7339000" />
      <workItem from="1720189013161" duration="609000" />
      <workItem from="1720198948635" duration="606000" />
      <workItem from="1720348162047" duration="679000" />
      <workItem from="1720372746545" duration="663000" />
      <workItem from="1720428751014" duration="1899000" />
      <workItem from="1720689827002" duration="612000" />
      <workItem from="1721125925974" duration="1206000" />
      <workItem from="1721161990684" duration="1283000" />
      <workItem from="1721173801905" duration="649000" />
      <workItem from="1721216316205" duration="1208000" />
      <workItem from="1721313342003" duration="2332000" />
      <workItem from="1721382958165" duration="1229000" />
      <workItem from="1721591176464" duration="357000" />
      <workItem from="1721648759690" duration="4380000" />
      <workItem from="1721689768675" duration="621000" />
      <workItem from="1721730381512" duration="1679000" />
      <workItem from="1721815463857" duration="9027000" />
      <workItem from="1721848771991" duration="1486000" />
      <workItem from="1721860245424" duration="1572000" />
      <workItem from="1721909721740" duration="12903000" />
      <workItem from="1721999370496" duration="5536000" />
      <workItem from="1722166942115" duration="1809000" />
      <workItem from="1722173140164" duration="6550000" />
      <workItem from="1722379930492" duration="3164000" />
      <workItem from="1722426834848" duration="26089000" />
      <workItem from="1722514264821" duration="8788000" />
      <workItem from="1722581384705" duration="7156000" />
      <workItem from="1722617034116" duration="2581000" />
      <workItem from="1722627771697" duration="2402000" />
      <workItem from="1722633503619" duration="4952000" />
      <workItem from="1722687667215" duration="5000" />
      <workItem from="1722866996473" duration="17482000" />
      <workItem from="1722933567150" duration="1180000" />
      <workItem from="1722934833101" duration="14830000" />
      <workItem from="1723026176250" duration="6193000" />
      <workItem from="1723124179332" duration="5694000" />
      <workItem from="1723131091883" duration="3179000" />
      <workItem from="1723152526870" duration="2537000" />
      <workItem from="1723208110757" duration="3693000" />
      <workItem from="1723296714397" duration="3660000" />
      <workItem from="1723467382657" duration="611000" />
      <workItem from="1723541965464" duration="1216000" />
      <workItem from="1723642529863" duration="22000" />
      <workItem from="1723935014059" duration="636000" />
      <workItem from="1724011788934" duration="3857000" />
      <workItem from="1724074991961" duration="3822000" />
      <workItem from="1724146726896" duration="3311000" />
      <workItem from="1724226799352" duration="3370000" />
      <workItem from="1724232300512" duration="13416000" />
      <workItem from="1724278752044" duration="3030000" />
      <workItem from="1724323876464" duration="13964000" />
      <workItem from="1724399068141" duration="1837000" />
      <workItem from="1724594157816" duration="2673000" />
      <workItem from="1724659870432" duration="3550000" />
      <workItem from="1724687642434" duration="1632000" />
      <workItem from="1724705130001" duration="732000" />
      <workItem from="1724747578423" duration="6771000" />
      <workItem from="1724828196085" duration="63000" />
      <workItem from="1724828276532" duration="2589000" />
      <workItem from="1724941847549" duration="7092000" />
      <workItem from="1725008776692" duration="11275000" />
      <workItem from="1725098249196" duration="627000" />
      <workItem from="1725265546527" duration="12025000" />
      <workItem from="1725287773123" duration="4989000" />
      <workItem from="1725319682920" duration="1119000" />
      <workItem from="1725353584406" duration="16304000" />
      <workItem from="1725439916172" duration="16387000" />
      <workItem from="1725549817609" duration="10023000" />
      <workItem from="1725959247619" duration="4641000" />
      <workItem from="1725968428066" duration="2655000" />
      <workItem from="1725986843784" duration="3439000" />
      <workItem from="1726041451667" duration="7026000" />
      <workItem from="1726068494095" duration="6000" />
      <workItem from="1726219963159" duration="3345000" />
      <workItem from="1726313457944" duration="2245000" />
      <workItem from="1726397130546" duration="1262000" />
      <workItem from="1726477147234" duration="6154000" />
      <workItem from="1726493789967" duration="1386000" />
      <workItem from="1726525349072" duration="3433000" />
      <workItem from="1726563347073" duration="10867000" />
      <workItem from="1726583741097" duration="7161000" />
      <workItem from="1726609054409" duration="3936000" />
      <workItem from="1726614181980" duration="3141000" />
      <workItem from="1726650309415" duration="5937000" />
      <workItem from="1726757614767" duration="2254000" />
      <workItem from="1726824041795" duration="16866000" />
      <workItem from="1727083801407" duration="1398000" />
      <workItem from="1727197671408" duration="1063000" />
      <workItem from="1727296358631" duration="6408000" />
      <workItem from="1727344445837" duration="27111000" />
      <workItem from="1727438238287" duration="18782000" />
      <workItem from="1727698917956" duration="1242000" />
      <workItem from="1727776919631" duration="641000" />
      <workItem from="1727789036355" duration="509000" />
      <workItem from="1727791183381" duration="2500000" />
      <workItem from="1727871679273" duration="722000" />
      <workItem from="1727947994542" duration="3566000" />
      <workItem from="1727964638913" duration="1119000" />
      <workItem from="1727977397501" duration="11000" />
      <workItem from="1727977622179" duration="1220000" />
      <workItem from="1727979544913" duration="1231000" />
      <workItem from="1727986892204" duration="1861000" />
      <workItem from="1727994685450" duration="6000" />
      <workItem from="1727994708225" duration="625000" />
      <workItem from="1728028070768" duration="3196000" />
      <workItem from="1728125661893" duration="7000" />
      <workItem from="1728133910398" duration="639000" />
      <workItem from="1728309712020" duration="1626000" />
      <workItem from="1728312866565" duration="1280000" />
      <workItem from="1728332947354" duration="2485000" />
      <workItem from="1728378578922" duration="10345000" />
      <workItem from="1728463026095" duration="12186000" />
      <workItem from="1728551169212" duration="1492000" />
      <workItem from="1728552673994" duration="2765000" />
      <workItem from="1728629851709" duration="20000" />
      <workItem from="1728646752668" duration="619000" />
      <workItem from="1728655919737" duration="11248000" />
      <workItem from="1728851207936" duration="713000" />
      <workItem from="1728898101885" duration="5631000" />
      <workItem from="1728974512711" duration="6693000" />
      <workItem from="1729083656853" duration="3866000" />
      <workItem from="1729121409377" duration="12000" />
      <workItem from="1729494913712" duration="3475000" />
      <workItem from="1729592940068" duration="2945000" />
      <workItem from="1729607328310" duration="4608000" />
      <workItem from="1729674199446" duration="2898000" />
      <workItem from="1729766132225" duration="2990000" />
      <workItem from="1729869048345" duration="6664000" />
      <workItem from="1729936481958" duration="923000" />
      <workItem from="1729950347110" duration="478000" />
      <workItem from="1729961610780" duration="1692000" />
      <workItem from="1729970551685" duration="1451000" />
      <workItem from="1730238860484" duration="1274000" />
      <workItem from="1730279604809" duration="6481000" />
      <workItem from="1730381180967" duration="16619000" />
      <workItem from="1730733263778" duration="9717000" />
      <workItem from="1730806289940" duration="18462000" />
      <workItem from="1730877751105" duration="11566000" />
      <workItem from="1730981637281" duration="17113000" />
      <workItem from="1731059040916" duration="4779000" />
      <workItem from="1731249120797" duration="818000" />
      <workItem from="1731330713622" duration="10713000" />
      <workItem from="1731408865890" duration="18602000" />
      <workItem from="1731493074196" duration="1625000" />
      <workItem from="1731581434606" duration="10008000" />
      <workItem from="1731668618222" duration="15982000" />
      <workItem from="1731765820219" duration="8164000" />
      <workItem from="1731924078363" duration="6731000" />
      <workItem from="1732011022644" duration="16302000" />
      <workItem from="1732100972429" duration="5503000" />
      <workItem from="1732135381500" duration="7000" />
      <workItem from="1732139345355" duration="4324000" />
      <workItem from="1732193560053" duration="6377000" />
      <workItem from="1732270571231" duration="4152000" />
      <workItem from="1732530786605" duration="2682000" />
      <workItem from="1732619001198" duration="6863000" />
      <workItem from="1732656350424" duration="665000" />
      <workItem from="1732730575768" duration="1982000" />
      <workItem from="1732794651328" duration="1857000" />
      <workItem from="1733145215128" duration="5043000" />
      <workItem from="1733220878682" duration="8293000" />
      <workItem from="1733308516807" duration="6140000" />
      <workItem from="1733397492053" duration="3646000" />
      <workItem from="1733483863562" duration="1132000" />
      <workItem from="1733737145527" duration="12014000" />
      <workItem from="1733833844828" duration="4776000" />
      <workItem from="1734088171627" duration="2899000" />
      <workItem from="1734102745593" duration="19000" />
      <workItem from="1734440052474" duration="793000" />
      <workItem from="1734529329895" duration="621000" />
      <workItem from="1734606682473" duration="4808000" />
      <workItem from="1735815641604" duration="2269000" />
      <workItem from="1735837365458" duration="9000" />
      <workItem from="1735901464655" duration="14113000" />
      <workItem from="1735995106542" duration="9520000" />
      <workItem from="1736078476466" duration="120000" />
      <workItem from="1737548425834" duration="5250000" />
      <workItem from="1737634798307" duration="3044000" />
      <workItem from="1737714614015" duration="7934000" />
      <workItem from="1737981622749" duration="4716000" />
      <workItem from="1738080134217" duration="5833000" />
      <workItem from="1738154061097" duration="16000" />
      <workItem from="1738154530066" duration="1376000" />
      <workItem from="1738229452495" duration="6167000" />
      <workItem from="1738354636313" duration="3058000" />
      <workItem from="1738519328122" duration="3734000" />
      <workItem from="1738577961216" duration="1206000" />
      <workItem from="1738617112637" duration="2277000" />
      <workItem from="1738673803784" duration="3657000" />
      <workItem from="1738746392827" duration="11159000" />
      <workItem from="1738849934449" duration="1011000" />
      <workItem from="1738935251154" duration="611000" />
      <workItem from="1739055803754" duration="848000" />
      <workItem from="1739132964972" duration="47000" />
      <workItem from="1739182989895" duration="5990000" />
      <workItem from="1739268972571" duration="1233000" />
      <workItem from="1739302227142" duration="1198000" />
      <workItem from="1739362308866" duration="2345000" />
      <workItem from="1739440790294" duration="3070000" />
      <workItem from="1739530733418" duration="2614000" />
      <workItem from="1739537411908" duration="1401000" />
      <workItem from="1739624726149" duration="1311000" />
      <workItem from="1739656623193" duration="485000" />
      <workItem from="1739786881467" duration="6978000" />
      <workItem from="1739866773184" duration="2099000" />
      <workItem from="1739883131084" duration="1148000" />
      <workItem from="1740410926443" duration="4482000" />
      <workItem from="1740564579765" duration="9461000" />
      <workItem from="1740599247114" duration="67000" />
      <workItem from="1740599456003" duration="741000" />
      <workItem from="1740648953353" duration="6022000" />
      <workItem from="1740740811253" duration="6300000" />
      <workItem from="1740828613800" duration="3084000" />
      <workItem from="1741007056431" duration="2484000" />
      <workItem from="1741085755558" duration="1388000" />
      <workItem from="1741171185591" duration="15072000" />
      <workItem from="1741293480877" duration="1077000" />
      <workItem from="1741344439207" duration="2183000" />
      <workItem from="1741689254266" duration="1879000" />
      <workItem from="1741774775091" duration="3284000" />
      <workItem from="1741865837007" duration="1411000" />
      <workItem from="1742039557112" duration="14225000" />
      <workItem from="1742057937142" duration="7999000" />
      <workItem from="1742209238431" duration="6042000" />
      <workItem from="1742292198355" duration="6247000" />
      <workItem from="1742376511030" duration="6425000" />
      <workItem from="1742658371094" duration="1337000" />
      <workItem from="1742812156628" duration="4063000" />
      <workItem from="1742902794628" duration="3685000" />
      <workItem from="1742979897272" duration="12559000" />
      <workItem from="1743077872884" duration="10000" />
      <workItem from="1743089364979" duration="8387000" />
      <workItem from="1743161135999" duration="5734000" />
      <workItem from="1743250434199" duration="12882000" />
      <workItem from="1743336136431" duration="913000" />
      <workItem from="1743337806747" duration="1960000" />
      <workItem from="1743345305602" duration="3443000" />
      <workItem from="1743415811470" duration="11040000" />
      <workItem from="1743500212241" duration="1943000" />
      <workItem from="1743596556488" duration="3959000" />
      <workItem from="1743673809123" duration="5526000" />
      <workItem from="1743760235811" duration="3458000" />
      <workItem from="1744023859915" duration="1782000" />
      <workItem from="1744105686885" duration="12989000" />
      <workItem from="1744193742976" duration="435000" />
      <workItem from="1744194431978" duration="1279000" />
      <workItem from="1744311455451" duration="1893000" />
      <workItem from="1744379241815" duration="6938000" />
      <workItem from="1744622733880" duration="9296000" />
      <workItem from="1744727140059" duration="2506000" />
      <workItem from="1744794920804" duration="3544000" />
      <workItem from="1744838789469" duration="770000" />
      <workItem from="1744889228874" duration="3149000" />
      <workItem from="1744967513664" duration="3075000" />
      <workItem from="1745312333395" duration="5577000" />
      <workItem from="1745349745007" duration="3203000" />
      <workItem from="1745400602121" duration="4289000" />
      <workItem from="1745495017411" duration="7739000" />
      <workItem from="1745576322628" duration="17474000" />
      <workItem from="1745663161990" duration="2344000" />
      <workItem from="1745832741029" duration="2241000" />
      <workItem from="1745922825359" duration="1514000" />
      <workItem from="1746622340784" duration="2130000" />
      <workItem from="1746707345666" duration="2967000" />
      <workItem from="1746790867954" duration="4260000" />
      <workItem from="1746873718485" duration="3243000" />
      <workItem from="1746962925519" duration="342000" />
      <workItem from="1747041652224" duration="4408000" />
      <workItem from="1747088375245" duration="41000" />
      <workItem from="1747128550392" duration="3840000" />
      <workItem from="1747172132021" duration="1442000" />
      <workItem from="1747223680645" duration="2115000" />
      <workItem from="1747311854498" duration="3123000" />
      <workItem from="1748343986143" duration="1919000" />
      <workItem from="1750158210708" duration="13783000" />
    </task>
    <task id="LOCAL-00239" summary="3">
      <option name="closed" value="true" />
      <created>1740567197571</created>
      <option name="number" value="00239" />
      <option name="presentableId" value="LOCAL-00239" />
      <option name="project" value="LOCAL" />
      <updated>1740567197571</updated>
    </task>
    <task id="LOCAL-00240" summary="4">
      <option name="closed" value="true" />
      <created>1740567279623</created>
      <option name="number" value="00240" />
      <option name="presentableId" value="LOCAL-00240" />
      <option name="project" value="LOCAL" />
      <updated>1740567279623</updated>
    </task>
    <task id="LOCAL-00241" summary="5">
      <option name="closed" value="true" />
      <created>1740567709476</created>
      <option name="number" value="00241" />
      <option name="presentableId" value="LOCAL-00241" />
      <option name="project" value="LOCAL" />
      <updated>1740567709476</updated>
    </task>
    <task id="LOCAL-00242" summary="normalized fps">
      <option name="closed" value="true" />
      <created>1740568431343</created>
      <option name="number" value="00242" />
      <option name="presentableId" value="LOCAL-00242" />
      <option name="project" value="LOCAL" />
      <updated>1740568431343</updated>
    </task>
    <task id="LOCAL-00243" summary="preview dwh">
      <option name="closed" value="true" />
      <created>1740568925130</created>
      <option name="number" value="00243" />
      <option name="presentableId" value="LOCAL-00243" />
      <option name="project" value="LOCAL" />
      <updated>1740568925130</updated>
    </task>
    <task id="LOCAL-00244" summary="before boost">
      <option name="closed" value="true" />
      <created>1740574230053</created>
      <option name="number" value="00244" />
      <option name="presentableId" value="LOCAL-00244" />
      <option name="project" value="LOCAL" />
      <updated>1740574230053</updated>
    </task>
    <task id="LOCAL-00245" summary=" game over screens, sounds test and more for flappy bird">
      <option name="closed" value="true" />
      <created>1740753789290</created>
      <option name="number" value="00245" />
      <option name="presentableId" value="LOCAL-00245" />
      <option name="project" value="LOCAL" />
      <updated>1740753789290</updated>
    </task>
    <task id="LOCAL-00246" summary="added new sounds to flappy.">
      <option name="closed" value="true" />
      <created>1741011199688</created>
      <option name="number" value="00246" />
      <option name="presentableId" value="LOCAL-00246" />
      <option name="project" value="LOCAL" />
      <updated>1741011199688</updated>
    </task>
    <task id="LOCAL-00247" summary="More game customization options">
      <option name="closed" value="true" />
      <created>1741179401261</created>
      <option name="number" value="00247" />
      <option name="presentableId" value="LOCAL-00247" />
      <option name="project" value="LOCAL" />
      <updated>1741179401261</updated>
    </task>
    <task id="LOCAL-00248" summary="set default preview screen">
      <option name="closed" value="true" />
      <created>1741180035658</created>
      <option name="number" value="00248" />
      <option name="presentableId" value="LOCAL-00248" />
      <option name="project" value="LOCAL" />
      <updated>1741180035658</updated>
    </task>
    <task id="LOCAL-00249" summary="poprawki loader gier">
      <option name="closed" value="true" />
      <created>1741186891829</created>
      <option name="number" value="00249" />
      <option name="presentableId" value="LOCAL-00249" />
      <option name="project" value="LOCAL" />
      <updated>1741186891829</updated>
    </task>
    <task id="LOCAL-00250" summary="build errors fix">
      <option name="closed" value="true" />
      <created>1741187291978</created>
      <option name="number" value="00250" />
      <option name="presentableId" value="LOCAL-00250" />
      <option name="project" value="LOCAL" />
      <updated>1741187291978</updated>
    </task>
    <task id="LOCAL-00251" summary="piwo piwo piwo">
      <option name="closed" value="true" />
      <created>1741209899544</created>
      <option name="number" value="00251" />
      <option name="presentableId" value="LOCAL-00251" />
      <option name="project" value="LOCAL" />
      <updated>1741209899544</updated>
    </task>
    <task id="LOCAL-00252" summary="piwooo">
      <option name="closed" value="true" />
      <created>1741210219096</created>
      <option name="number" value="00252" />
      <option name="presentableId" value="LOCAL-00252" />
      <option name="project" value="LOCAL" />
      <updated>1741210219096</updated>
    </task>
    <task id="LOCAL-00253" summary="w chuj poprawek flappy">
      <option name="closed" value="true" />
      <created>1741815359064</created>
      <option name="number" value="00253" />
      <option name="presentableId" value="LOCAL-00253" />
      <option name="project" value="LOCAL" />
      <updated>1741815359065</updated>
    </task>
    <task id="LOCAL-00254" summary="dziura wieksza">
      <option name="closed" value="true" />
      <created>1741816610884</created>
      <option name="number" value="00254" />
      <option name="presentableId" value="LOCAL-00254" />
      <option name="project" value="LOCAL" />
      <updated>1741816610884</updated>
    </task>
    <task id="LOCAL-00255" summary="dzuiura">
      <option name="closed" value="true" />
      <created>1741816637900</created>
      <option name="number" value="00255" />
      <option name="presentableId" value="LOCAL-00255" />
      <option name="project" value="LOCAL" />
      <updated>1741816637900</updated>
    </task>
    <task id="LOCAL-00256" summary="Before responsive refactor.">
      <option name="closed" value="true" />
      <created>1741872992094</created>
      <option name="number" value="00256" />
      <option name="presentableId" value="LOCAL-00256" />
      <option name="project" value="LOCAL" />
      <updated>1741872992094</updated>
    </task>
    <task id="LOCAL-00257" summary="Dockerfile locations changed.">
      <option name="closed" value="true" />
      <created>1742039810733</created>
      <option name="number" value="00257" />
      <option name="presentableId" value="LOCAL-00257" />
      <option name="project" value="LOCAL" />
      <updated>1742039810733</updated>
    </task>
    <task id="LOCAL-00258" summary="Fixed Dockerfile">
      <option name="closed" value="true" />
      <created>1742040683463</created>
      <option name="number" value="00258" />
      <option name="presentableId" value="LOCAL-00258" />
      <option name="project" value="LOCAL" />
      <updated>1742040683463</updated>
    </task>
    <task id="LOCAL-00259" summary="Fixed build errors">
      <option name="closed" value="true" />
      <created>1742041826071</created>
      <option name="number" value="00259" />
      <option name="presentableId" value="LOCAL-00259" />
      <option name="project" value="LOCAL" />
      <updated>1742041826071</updated>
    </task>
    <task id="LOCAL-00260" summary="Reverted dockerfile handling. Added fly.toml instead">
      <option name="closed" value="true" />
      <created>1742042349810</created>
      <option name="number" value="00260" />
      <option name="presentableId" value="LOCAL-00260" />
      <option name="project" value="LOCAL" />
      <updated>1742042349810</updated>
    </task>
    <task id="LOCAL-00261" summary="test">
      <option name="closed" value="true" />
      <created>1742044203815</created>
      <option name="number" value="00261" />
      <option name="presentableId" value="LOCAL-00261" />
      <option name="project" value="LOCAL" />
      <updated>1742044203815</updated>
    </task>
    <task id="LOCAL-00262" summary="tst 2">
      <option name="closed" value="true" />
      <created>1742044234176</created>
      <option name="number" value="00262" />
      <option name="presentableId" value="LOCAL-00262" />
      <option name="project" value="LOCAL" />
      <updated>1742044234176</updated>
    </task>
    <task id="LOCAL-00263" summary="fixed fly.toml file">
      <option name="closed" value="true" />
      <created>1742047936486</created>
      <option name="number" value="00263" />
      <option name="presentableId" value="LOCAL-00263" />
      <option name="project" value="LOCAL" />
      <updated>1742047936486</updated>
    </task>
    <task id="LOCAL-00264" summary="optimized build step for docker">
      <option name="closed" value="true" />
      <created>1742048534749</created>
      <option name="number" value="00264" />
      <option name="presentableId" value="LOCAL-00264" />
      <option name="project" value="LOCAL" />
      <updated>1742048534749</updated>
    </task>
    <task id="LOCAL-00265" summary="test 4">
      <option name="closed" value="true" />
      <created>1742048616574</created>
      <option name="number" value="00265" />
      <option name="presentableId" value="LOCAL-00265" />
      <option name="project" value="LOCAL" />
      <updated>1742048616574</updated>
    </task>
    <task id="LOCAL-00266" summary="test and fly file improvements. machine now sleeps">
      <option name="closed" value="true" />
      <created>1742062856774</created>
      <option name="number" value="00266" />
      <option name="presentableId" value="LOCAL-00266" />
      <option name="project" value="LOCAL" />
      <updated>1742062856775</updated>
    </task>
    <task id="LOCAL-00267" summary="Editor previewscene moved to react now">
      <option name="closed" value="true" />
      <created>1742224252639</created>
      <option name="number" value="00267" />
      <option name="presentableId" value="LOCAL-00267" />
      <option name="project" value="LOCAL" />
      <updated>1742224252639</updated>
    </task>
    <task id="LOCAL-00268" summary="Click events fix.">
      <option name="closed" value="true" />
      <created>1742225191558</created>
      <option name="number" value="00268" />
      <option name="presentableId" value="LOCAL-00268" />
      <option name="project" value="LOCAL" />
      <updated>1742225191558</updated>
    </task>
    <task id="LOCAL-00269" summary="flappy improvements">
      <option name="closed" value="true" />
      <created>1742304160949</created>
      <option name="number" value="00269" />
      <option name="presentableId" value="LOCAL-00269" />
      <option name="project" value="LOCAL" />
      <updated>1742304160949</updated>
    </task>
    <task id="LOCAL-00270" summary="before new upscale">
      <option name="closed" value="true" />
      <created>1742333908306</created>
      <option name="number" value="00270" />
      <option name="presentableId" value="LOCAL-00270" />
      <option name="project" value="LOCAL" />
      <updated>1742333908306</updated>
    </task>
    <task id="LOCAL-00271" summary="ddf">
      <option name="closed" value="true" />
      <created>1742380178064</created>
      <option name="number" value="00271" />
      <option name="presentableId" value="LOCAL-00271" />
      <option name="project" value="LOCAL" />
      <updated>1742380178065</updated>
    </task>
    <task id="LOCAL-00272" summary="trawa wyzej">
      <option name="closed" value="true" />
      <created>1742381369203</created>
      <option name="number" value="00272" />
      <option name="presentableId" value="LOCAL-00272" />
      <option name="project" value="LOCAL" />
      <updated>1742381369203</updated>
    </task>
    <task id="LOCAL-00273" summary="sdfsda">
      <option name="closed" value="true" />
      <created>1742415397723</created>
      <option name="number" value="00273" />
      <option name="presentableId" value="LOCAL-00273" />
      <option name="project" value="LOCAL" />
      <updated>1742415397724</updated>
    </task>
    <task id="LOCAL-00274" summary="ffg">
      <option name="closed" value="true" />
      <created>1742417041321</created>
      <option name="number" value="00274" />
      <option name="presentableId" value="LOCAL-00274" />
      <option name="project" value="LOCAL" />
      <updated>1742417041321</updated>
    </task>
    <task id="LOCAL-00275" summary="Before physics">
      <option name="closed" value="true" />
      <created>1742824803289</created>
      <option name="number" value="00275" />
      <option name="presentableId" value="LOCAL-00275" />
      <option name="project" value="LOCAL" />
      <updated>1742824803289</updated>
    </task>
    <task id="LOCAL-00276" summary="before 2">
      <option name="closed" value="true" />
      <created>1742824989898</created>
      <option name="number" value="00276" />
      <option name="presentableId" value="LOCAL-00276" />
      <option name="project" value="LOCAL" />
      <updated>1742824989898</updated>
    </task>
    <task id="LOCAL-00277" summary="edytor lives handler, flappy bird poprawki prawie ostateczne">
      <option name="closed" value="true" />
      <created>1742996946643</created>
      <option name="number" value="00277" />
      <option name="presentableId" value="LOCAL-00277" />
      <option name="project" value="LOCAL" />
      <updated>1742996946643</updated>
    </task>
    <task id="LOCAL-00278" summary="No useQuery in usegamemodule hook.">
      <option name="closed" value="true" />
      <created>1742998283564</created>
      <option name="number" value="00278" />
      <option name="presentableId" value="LOCAL-00278" />
      <option name="project" value="LOCAL" />
      <updated>1742998283564</updated>
    </task>
    <task id="LOCAL-00279" summary="Lives handler poprawki.">
      <option name="closed" value="true" />
      <created>1742999102689</created>
      <option name="number" value="00279" />
      <option name="presentableId" value="LOCAL-00279" />
      <option name="project" value="LOCAL" />
      <updated>1742999102689</updated>
    </task>
    <task id="LOCAL-00280" summary="visibility settings fixed from config.">
      <option name="closed" value="true" />
      <created>1742999993990</created>
      <option name="number" value="00280" />
      <option name="presentableId" value="LOCAL-00280" />
      <option name="project" value="LOCAL" />
      <updated>1742999993990</updated>
    </task>
    <task id="LOCAL-00281" summary="new flappy poc">
      <option name="closed" value="true" />
      <created>1743110819932</created>
      <option name="number" value="00281" />
      <option name="presentableId" value="LOCAL-00281" />
      <option name="project" value="LOCAL" />
      <updated>1743110819933</updated>
    </task>
    <task id="LOCAL-00282" summary="2048 alpha, TEMPORARILY disabled pooling for postgres.">
      <option name="closed" value="true" />
      <created>1743288329954</created>
      <option name="number" value="00282" />
      <option name="presentableId" value="LOCAL-00282" />
      <option name="project" value="LOCAL" />
      <updated>1743288329954</updated>
    </task>
    <task id="LOCAL-00283" summary="New  2048 game">
      <option name="closed" value="true" />
      <created>1743421129096</created>
      <option name="number" value="00283" />
      <option name="presentableId" value="LOCAL-00283" />
      <option name="project" value="LOCAL" />
      <updated>1743421129097</updated>
    </task>
    <task id="LOCAL-00284" summary="sadf">
      <option name="closed" value="true" />
      <created>1743421696980</created>
      <option name="number" value="00284" />
      <option name="presentableId" value="LOCAL-00284" />
      <option name="project" value="LOCAL" />
      <updated>1743421696980</updated>
    </task>
    <task id="LOCAL-00285" summary="slots game">
      <option name="closed" value="true" />
      <created>1744112357478</created>
      <option name="number" value="00285" />
      <option name="presentableId" value="LOCAL-00285" />
      <option name="project" value="LOCAL" />
      <updated>1744112357478</updated>
    </task>
    <task id="LOCAL-00286" summary="fdsfsda">
      <option name="closed" value="true" />
      <created>1744314202499</created>
      <option name="number" value="00286" />
      <option name="presentableId" value="LOCAL-00286" />
      <option name="project" value="LOCAL" />
      <updated>1744314202499</updated>
    </task>
    <task id="LOCAL-00287" summary="Sound toggle">
      <option name="closed" value="true" />
      <created>1744383382173</created>
      <option name="number" value="00287" />
      <option name="presentableId" value="LOCAL-00287" />
      <option name="project" value="LOCAL" />
      <updated>1744383382173</updated>
    </task>
    <option name="localTasksCounter" value="288" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="GitHub.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="663af5c9-13d2-41be-aa40-e461d806df37" value="TOOL_WINDOW" />
        <entry key="cfb9cbc9-b047-416c-a7c7-78737ba65475" value="TOOL_WINDOW" />
        <entry key="d9510568-a22e-4875-9d8a-9b0238c5dd09" value="TOOL_WINDOW" />
        <entry key="fedbf92c-28d6-4db4-b368-3b027a260dac" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="663af5c9-13d2-41be-aa40-e461d806df37">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1/couponapp-client" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="cfb9cbc9-b047-416c-a7c7-78737ba65475">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1/CouponApp.Server" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="d9510568-a22e-4875-9d8a-9b0238c5dd09">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="fedbf92c-28d6-4db4-b368-3b027a260dac">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="structure">
                    <value>
                      <list>
                        <option value="dir:C:/Users/<USER>/RiderProjects/ReactIdentity/ReactApp1" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="fixed fly.toml file" />
    <MESSAGE value="optimized build step for docker" />
    <MESSAGE value="test 4" />
    <MESSAGE value="test and fly file improvements. machine now sleeps" />
    <MESSAGE value="Editor previewscene moved to react now" />
    <MESSAGE value="Click events fix." />
    <MESSAGE value="flappy improvements" />
    <MESSAGE value="before new upscale" />
    <MESSAGE value="ddf" />
    <MESSAGE value="trawa wyzej" />
    <MESSAGE value="sdfsda" />
    <MESSAGE value="ffg" />
    <MESSAGE value="Before physics" />
    <MESSAGE value="before 2" />
    <MESSAGE value="edytor lives handler, flappy bird poprawki prawie ostateczne" />
    <MESSAGE value="No useQuery in usegamemodule hook." />
    <MESSAGE value="Lives handler poprawki." />
    <MESSAGE value="visibility settings fixed from config." />
    <MESSAGE value="new flappy poc" />
    <MESSAGE value="2048 alpha, TEMPORARILY disabled pooling for postgres." />
    <MESSAGE value="New  2048 game" />
    <MESSAGE value="sadf" />
    <MESSAGE value="slots game" />
    <MESSAGE value="fdsfsda" />
    <MESSAGE value="Sound toggle" />
    <option name="LAST_COMMIT_MESSAGE" value="Sound toggle" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/CouponApp.Server/Features/Plausible/PlausibleService.cs</url>
          <line>92</line>
          <properties documentPath="C:\Users\<USER>\RiderProjects\ReactIdentity\ReactApp1\CouponApp.Server\Services\PlausibleService.cs" containingFunctionPresentation="Method 'SendPlausibleRequest'">
            <startOffsets>
              <option value="3549" />
            </startOffsets>
            <endOffsets>
              <option value="3647" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="12" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="13" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="14" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>